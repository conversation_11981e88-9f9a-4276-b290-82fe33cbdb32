import telethon
from telethon.sync import TelegramClient
from telethon.tl.functions.messages import GetDialogsRequest
from telethon.tl.types import InputPeerEmpty
import pandas as pd
import time
from datetime import datetime, timedelta

class UAECryptoTraderFinder:
    def __init__(self):
        # Telegram API
        self.api_id = 24329138
        self.api_hash = "978c2357b7891d2f06977a567263240b"
        self.phone_number = "+22242001914"
        self.client = None
        
        # Trading keywords (expanded list)
        self.buy_keywords = ["buy", "want", "need", "looking for", "purchase"]
        self.sell_keywords = ["sell", "selling", "exchange", "swap", "trade"]
        self.crypto_keywords = ["usdt", "bitcoin", "btc", "crypto", "tether"]
        self.uae_keywords = ["uae", "dubai", "abudhabi", "aed", "dxb", "emirates"]
        
        # Storage
        self.today_traders = []
        self.last_run = None

    def connect(self):
        """Reliable connection with session management"""
        try:
            self.client = TelegramClient('uae_trader_session', self.api_id, self.api_hash)
            self.client.connect()
            
            if not self.client.is_user_authorized():
                self.client.send_code_request(self.phone_number)
                self.client.sign_in(self.phone_number, input("Enter Telegram code: "))
            
            return True
        except Exception as e:
            print(f"🔴 Connection failed: {str(e)}")
            return False

    def is_uae_trader(self, text):
        """Advanced message analysis"""
        text = text.lower()
        
        # Must contain crypto + UAE references
        has_crypto = any(kw in text for kw in self.crypto_keywords)
        has_uae = any(kw in text for kw in self.uae_keywords)
        
        if not (has_crypto and has_uae):
            return None
        
        # Determine buyer/seller
        is_buyer = any(kw in text for kw in self.buy_keywords)
        is_seller = any(kw in text for kw in self.sell_keywords)
        
        if is_buyer and is_seller:
            return "both"
        elif is_buyer:
            return "buyer"
        elif is_seller:
            return "seller"
        return None

    def scan_all_dialogs(self):
        """Scan all chats/groups/channels"""
        if not self.client:
            return []
            
        traders = []
        dialogs = self.client(GetDialogsRequest(
            offset_date=None,
            offset_id=0,
            offset_peer=InputPeerEmpty(),
            limit=200,
            hash=0
        )).dialogs
        
        for dialog in dialogs:
            try:
                entity = self.client.get_entity(dialog.peer)
                
                # Skip private chats
                if not hasattr(entity, 'megagroup') and not (hasattr(entity, 'broadcast')):
                    continue
                
                print(f"🔍 Scanning {entity.title}...")
                
                # Check recent messages (last 6 hours)
                for msg in self.client.iter_messages(entity, limit=50, 
                                                  offset_date=datetime.now() - timedelta(hours=6)):
                    if msg.text:
                        trader_type = self.is_uae_trader(msg.text)
                        if trader_type:
                            traders.append({
                                'type': trader_type,
                                'username': msg.sender.username if msg.sender else "N/A",
                                'text': msg.text[:200],
                                'date': msg.date,
                                'chat': entity.title,
                                'chat_link': f"https://t.me/c/{entity.id}/{msg.id}"
                            })
                
                time.sleep(5)  # Rate limiting
                
            except Exception as e:
                print(f"⚠️ Error in {getattr(entity, 'title', 'Unknown')}: {str(e)}")
                continue
                
        return traders

    def save_results(self, traders):
        """Save to CSV with timestamp"""
        if not traders:
            print("❌ No traders found this scan")
            return
            
        df = pd.DataFrame(traders)
        filename = f"uae_traders_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        df.to_csv(filename, index=False)
        print(f"💾 Saved {len(df)} traders to {filename}")
        
        # Keep track of today's findings
        self.today_traders.extend(traders)
        self.last_run = datetime.now()

    def daily_report(self):
        """Generate daily summary"""
        if not self.today_traders:
            print("No traders found today")
            return
            
        df = pd.DataFrame(self.today_traders)
        
        # Daily stats
        print(f"\n📊 Daily Report ({datetime.now().date()})")
        print(f"Total traders found: {len(df)}")
        print(f"Buyers: {len(df[df['type'] == 'buyer'])}")
        print(f"Sellers: {len(df[df['type'] == 'seller'])}")
        print(f"Both: {len(df[df['type'] == 'both'])}")
        
        # Save daily compilation
        df.to_csv(f"uae_traders_daily_{datetime.now().date()}.csv", index=False)
        self.today_traders = []  # Reset for new day

    def run_scan(self):
        print(f"\n⏰ Starting scan at {datetime.now()}")
        if not self.connect():
            return
            
        try:
            traders = self.scan_all_dialogs()
            self.save_results(traders)
            
            # Send daily report at 8 PM
            if datetime.now().hour == 20:
                self.daily_report()
                
        except Exception as e:
            print(f"🔴 Scan failed: {str(e)}")
        finally:
            if self.client:
                self.client.disconnect()
            print(f"⌛ Scan completed at {datetime.now()}")

if __name__ == "__main__":
    finder = UAECryptoTraderFinder()

    # Run scan once
    finder.run_scan()