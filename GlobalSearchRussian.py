import telethon
from telethon.sync import TelegramClient
from telethon.tl.functions.messages import GetDialogsRequest, SearchRequest
from telethon.tl.types import InputPeerEmpty
import pandas as pd
import time
from datetime import datetime, timedelta
import schedule
import requests
import re

class AdvancedCryptoTraderFinder:
    def __init__(self):
        # Telegram API
        self.api_id = 24329138
        self.api_hash = "978c2357b7891d2f06977a567263240b"
        self.phone_number = "+22242001914"
        self.client = None
        
        # DeepSeek API
        self.deepseek_key = "your_deepseek_api_key"
        self.deepseek_url = "https://api.deepseek.com/v1/chat/completions"
        
        # Trading parameters
        self.crypto_keywords = ["usdt", "btc", "bitcoin", "crypto", "tether"]
        self.cash_keywords = ["cash", "fiat", "aed", "dirham", "dollars", "рубли", "деньги"]
        self.location_keywords = {
            'uae': ["uae", "dubai", "abudhabi", "dxb", "emirates", "абу-даби"],
            'russia': ["russia", "moscow", "россия", "москва", "spb", "питер"]
        }
        
        # Storage
        self.all_traders = []
        self.translation_cache = {}

    def connect(self):
        """Enhanced connection handler"""
        try:
            self.client = TelegramClient('crypto_trader_session', self.api_id, self.api_hash)
            self.client.connect()
            
            if not self.client.is_user_authorized():
                self.client.send_code_request(self.phone_number)
                code = input("Enter Telegram code: ")
                self.client.sign_in(self.phone_number, code)
            
            print("✅ Connected to Telegram")
            return True
        except Exception as e:
            print(f"🔴 Connection failed: {str(e)}")
            return False

    def translate_with_deepseek(self, text, target_lang="en"):
        """Translate text using DeepSeek AI"""
        if text in self.translation_cache:
            return self.translation_cache[text]
            
        headers = {
            "Authorization": f"Bearer {self.deepseek_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": [{
                "role": "user",
                "content": f"Translate this to {target_lang} exactly without commentary: {text}"
            }],
            "temperature": 0.1
        }
        
        try:
            response = requests.post(self.deepseek_url, json=payload, headers=headers)
            translation = response.json()['choices'][0]['message']['content']
            self.translation_cache[text] = translation
            return translation
        except Exception as e:
            print(f"⚠️ Translation failed: {str(e)}")
            return text

    def analyze_with_deepseek(self, text):
        """Advanced analysis with DeepSeek"""
        headers = {
            "Authorization": f"Bearer {self.deepseek_key}",
            "Content-Type": "application/json"
        }
        
        prompt = f"""
        Analyze this crypto trading message:
        {text}
        
        Return JSON with:
        - is_crypto_to_cash (bool)
        - is_cash_to_crypto (bool)
        - amount (string)
        - currencies (list)
        - location (string)
        - intent_confidence (1-10)
        """
        
        payload = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "response_format": {"type": "json_object"},
            "temperature": 0.3
        }
        
        try:
            response = requests.post(self.deepseek_url, json=payload, headers=headers)
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"⚠️ DeepSeek analysis failed: {str(e)}")
            return None

    def scan_global_messages(self, query, limit=20):
        """Search across all Telegram"""
        try:
            results = []
            search = self.client(SearchRequest(
                peer=None,
                q=query,
                filter=InputPeerEmpty(),
                min_date=datetime.now() - timedelta(days=3),
                limit=limit
            ))
            
            for msg in search.messages:
                if msg.text:
                    original_text = msg.text
                    translated = self.translate_with_deepseek(original_text)
                    
                    analysis = self.analyze_with_deepseek(translated)
                    if analysis and analysis.get('is_crypto_to_cash', False):
                        results.append({
                            'original_text': original_text,
                            'translated': translated,
                            'analysis': analysis,
                            'date': msg.date,
                            'sender': msg.sender_id,
                            'is_russian': any(re.search(r'[\u0400-\u04FF]', original_text)),
                            'is_uae': 'uae' in analysis.get('location', '').lower()
                        })
            return results
        except Exception as e:
            print(f"⚠️ Global search failed: {str(e)}")
            return []

    def run_scan(self):
        print(f"\n🔍 Starting scan at {datetime.now()}")
        if not self.connect():
            return
            
        try:
            # 1. Scan global for cash traders
            print("🌍 Searching for cash traders globally...")
            cash_traders = []
            for query in ["sell USDT for cash", "обменять BTC на наличные"]:
                traders = self.scan_global_messages(query)
                cash_traders.extend(traders)
                time.sleep(5)  # Rate limit
            
            # 2. Save results
            if cash_traders:
                df = pd.DataFrame([{
                    'date': t['date'],
                    'is_russian': t['is_russian'],
                    'is_uae': t['is_uae'],
                    'original_text': t['original_text'],
                    'translated': t['translated'],
                    'amount': t['analysis'].get('amount'),
                    'currencies': ", ".join(t['analysis'].get('currencies', [])),
                    'intent_confidence': t['analysis'].get('intent_confidence'),
                    'location': t['analysis'].get('location')
                } for t in cash_traders])
                
                filename = f"cash_traders_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
                df.to_csv(filename, index=False)
                print(f"💾 Saved {len(df)} cash traders to {filename}")
            else:
                print("❌ No cash traders found")
                
        except Exception as e:
            print(f"🔴 Scan failed: {str(e)}")
        finally:
            if self.client:
                self.client.disconnect()

    def schedule_scans(self):
        """Run every 6 hours"""
        schedule.every(6).hours.do(self.run_scan)
        
        # Immediate first run
        self.run_scan()
        
        while True:
            schedule.run_pending()
            time.sleep(60)

if __name__ == "__main__":
    finder = AdvancedCryptoTraderFinder()
    finder.schedule_scans()