import telethon
from telethon.sync import TelegramClient
from telethon.tl.functions.messages import GetDialogsRequest, SearchRequest
from telethon.tl.types import InputPeerEmpty
import pandas as pd
import time
from datetime import datetime, timedelta
import requests
import re

class ManualCryptoTraderFinder:
    def __init__(self):
        # Telegram API
        self.api_id = 24329138
        self.api_hash = "978c2357b7891d2f06977a567263240b"
        self.phone_number = "+22242001914"
        self.client = None
        
        # DeepSeek API
        self.deepseek_key = "your_deepseek_api_key"
        self.deepseek_url = "https://api.deepseek.com/v1/chat/completions"
        
        # Trading parameters
        self.search_queries = [
            "sell USDT for cash UAE",
            "buy BTC with AED",
            "обменять USDT на наличные",
            "куплю биткоин за рубли"
        ]
        self.max_results = 50  # Per query

    def connect(self):
        """Simplified connection handler"""
        try:
            self.client = TelegramClient('manual_session', self.api_id, self.api_hash)
            self.client.connect()
            
            if not self.client.is_user_authorized():
                self.client.send_code_request(self.phone_number)
                self.client.sign_in(self.phone_number, input("Enter Telegram code: "))
            
            print("✅ Connected to Telegram")
            return True
        except Exception as e:
            print(f"🔴 Connection failed: {str(e)}")
            return False

    def translate_text(self, text, target_lang="en"):
        """Translate using DeepSeek AI"""
        headers = {
            "Authorization": f"Bearer {self.deepseek_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "deepseek-chat",
            "messages": [{
                "role": "user",
                "content": f"Translate this to {target_lang} exactly: {text}"
            }],
            "temperature": 0.1
        }
        
        try:
            response = requests.post(self.deepseek_url, json=payload, headers=headers)
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"⚠️ Translation failed: {str(e)}")
            return text

    def analyze_trade(self, text):
        """Detect trade details with DeepSeek"""
        headers = {
            "Authorization": f"Bearer {self.deepseek_key}",
            "Content-Type": "application/json"
        }
        
        prompt = f"""
        Analyze this crypto trading message:
        {text}
        
        Return JSON with these keys:
        - trade_type (buy/sell/exchange)
        - crypto_currency (USDT/BTC/etc)
        - fiat_currency (AED/RUB/etc)
        - amount (number)
        - location (city/country)
        - is_cash (bool)
        - confidence (1-10)
        """
        
        payload = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "response_format": {"type": "json_object"},
            "temperature": 0.3
        }
        
        try:
            response = requests.post(self.deepseek_url, json=payload, headers=headers)
            return response.json()['choices'][0]['message']['content']
        except Exception as e:
            print(f"⚠️ Analysis failed: {str(e)}")
            return None

    def search_traders(self):
        """Manual search with enhanced filters"""
        all_traders = []
        
        for query in self.search_queries:
            print(f"\n🔍 Searching: '{query}'...")
            try:
                results = self.client(SearchRequest(
                    peer=None,  # Global search
                    q=query,
                    filter=InputPeerEmpty(),
                    min_date=datetime.now() - timedelta(days=3),
                    limit=self.max_results
                ))
                
                for msg in results.messages:
                    if not msg.text:
                        continue
                        
                    # Translate if Russian detected
                    original_text = msg.text
                    is_russian = bool(re.search(r'[\u0400-\u04FF]', original_text))
                    translated = self.translate_text(original_text) if is_russian else original_text
                    
                    # Deep analysis
                    analysis = self.analyze_trade(translated)
                    if not analysis:
                        continue
                        
                    trader_data = {
                        'date': msg.date,
                        'original_text': original_text,
                        'translated': translated,
                        'is_russian': is_russian,
                        'is_uae': 'dubai' in translated.lower() or 'uae' in translated.lower(),
                        'analysis': analysis
                    }
                    all_traders.append(trader_data)
                    
                    # Preview finding
                    print(f"\n📍 Found trader ({msg.date}):")
                    print(f"   Original: {original_text[:100]}...")
                    print(f"   Analysis: {analysis}")
                    
                time.sleep(5)  # Rate limiting
                
            except Exception as e:
                print(f"⚠️ Search failed for '{query}': {str(e)}")
                continue
                
        return all_traders

    def save_results(self, traders):
        """Save to CSV with clean formatting"""
        if not traders:
            print("❌ No traders found")
            return
            
        # Prepare structured data
        structured_data = []
        for t in traders:
            try:
                structured_data.append({
                    'datetime': t['date'].strftime("%Y-%m-%d %H:%M"),
                    'is_russian': t['is_russian'],
                    'is_uae': t['is_uae'],
                    'trade_type': t['analysis'].get('trade_type'),
                    'crypto': t['analysis'].get('crypto_currency'),
                    'fiat': t['analysis'].get('fiat_currency'),
                    'amount': t['analysis'].get('amount'),
                    'location': t['analysis'].get('location'),
                    'confidence': t['analysis'].get('confidence'),
                    'original_text': t['original_text'][:200],
                    'translated': t['translated'][:200],
                    'is_cash': t['analysis'].get('is_cash', False)
                })
            except Exception as e:
                print(f"⚠️ Error processing trader: {str(e)}")
                continue
                
        df = pd.DataFrame(structured_data)
        filename = f"crypto_traders_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
        df.to_csv(filename, index=False)
        print(f"\n💾 Saved {len(df)} traders to {filename}")

    def run(self):
        """Manual execution flow"""
        print("\n=== MANUAL CRYPTO TRADER FINDER ===")
        print("Scanning for cash traders in UAE/Russia...\n")
        
        if not self.connect():
            return
            
        try:
            start_time = time.time()
            traders = self.search_traders()
            self.save_results(traders)
            
            print(f"\n⏱️ Completed in {time.time()-start_time:.1f} seconds")
            if traders:
                print("🔍 Search Tips:")
                print("- Check CSV for full details")
                print("- Sort by 'confidence' column")
                print("- Filter 'is_cash=True' for cash deals")
                
        except Exception as e:
            print(f"\n🔴 Fatal error: {str(e)}")
        finally:
            if self.client:
                self.client.disconnect()
            print("\nSession ended")

if __name__ == "__main__":
    finder = ManualCryptoTraderFinder()
    finder.run()